<template>
  <div class="system-form">
    <div class="simple-title">{{ pageTitle }}</div>
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
            基本信息
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerBasicForm" :schemas="dynamicBasicInfoSchema" />
        </div>
      </div>

      <!-- 附件信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:paper-clip-outlined" class="title-icon" />
            附件信息
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerAttachmentForm" :schemas="attachmentInfoSchema" />
          <!-- 文件上传提示信息 -->
          <div class="upload-tips">
            <div class="upload-tip-item">
              <span>支持的文件格式：.doc、.docx、.xls、.xlsx、.pdf、.png、.jpeg、.jpg</span>
            </div>
            <div class="upload-tip-item">
              <span>单个文件最大不超过50MB，可以上传多个附件</span>
            </div>
            <div class="upload-tip-item">
              <span>请上传制度相关的正式文件，确保文件内容准确完整</span>
            </div>
          </div>
          <!-- 已上传文件列表 -->
          <div v-if="uploadedFiles.length > 0" class="uploaded-files">
            <div class="files-title">
              <Icon icon="ant-design:file-done-outlined" class="title-icon" />
              已上传文件 ({{ uploadedFiles.length }})
            </div>
            <div class="files-list">
              <div v-for="(file, index) in uploadedFiles" :key="index" class="file-item">
                <div class="file-info">
                  <Icon :icon="getFileIcon(file.name)" class="file-icon" />
                  <span class="file-name">{{ file.name }}</span>
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                </div>
                <div class="file-actions">
                  <a-button type="link" size="small" @click="previewFile(file)">
                    <Icon icon="ant-design:eye-outlined" />
                    预览
                  </a-button>
                  <a-button type="link" size="small" @click="downloadFile(file)">
                    <Icon icon="ant-design:download-outlined" />
                    下载
                  </a-button>
                  <a-button type="link" size="small" danger @click="removeFile(index)">
                    <Icon icon="ant-design:delete-outlined" />
                    删除
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单提交按钮 -->
      <div class="form-footer">
        <a-button @click="handleReset">重置</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="loading" style="margin-left: 12px">提交</a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="SystemManageForm" setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { basicInfoSchema, attachmentInfoSchema, getStatusOptions } from './systemForm.data';
  import { getDetail, saveOrUpdate } from './system.api';

  const route = useRoute();
  const router = useRouter();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');
  const originalStatus = ref<number | null>(null);
  const uploadedFiles = ref<any[]>([]);

  // 注册表单
  const [
    registerBasicForm,
    { resetFields: _resetBasicFields, setFieldsValue: setBasicFieldsValue, validate: validateBasic, getFieldsValue: getBasicFieldsValue },
  ] = useForm({
    labelWidth: 180,
    baseColProps: { span: 8 },
    showActionButtonGroup: false,
    schemas: basicInfoSchema,
  });

  const [
    registerAttachmentForm,
    {
      resetFields: _resetAttachmentFields,
      setFieldsValue: setAttachmentFieldsValue,
      validate: validateAttachment,
      getFieldsValue: getAttachmentFieldsValue,
    },
  ] = useForm({
    labelWidth: 180,
    baseColProps: { span: 24 },
    showActionButtonGroup: false,
    schemas: attachmentInfoSchema,
  });

  // 计算属性：页面标题
  const pageTitle = computed(() => {
    return isUpdate.value ? '编辑制度信息' : '新增制度信息';
  });

  // 计算属性：动态基本信息表单schema（包含状态选项）
  const dynamicBasicInfoSchema = computed(() => {
    const schema = [...basicInfoSchema];
    const statusField = schema.find((item) => item.field === 'status');
    if (statusField) {
      statusField.componentProps = {
        ...statusField.componentProps,
        options: getStatusOptions(isUpdate.value, originalStatus.value || undefined),
      };
    }
    return schema;
  });

  // 获取文件图标
  function getFileIcon(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'doc':
      case 'docx':
        return 'ant-design:file-word-outlined';
      case 'xls':
      case 'xlsx':
        return 'ant-design:file-excel-outlined';
      case 'pdf':
        return 'ant-design:file-pdf-outlined';
      case 'png':
      case 'jpeg':
      case 'jpg':
        return 'ant-design:file-image-outlined';
      default:
        return 'ant-design:file-outlined';
    }
  }

  // 格式化文件大小
  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 预览文件
  function previewFile(file: any) {
    if (file.url) {
      window.open(file.url, '_blank');
    } else {
      createMessage.info('文件预览功能暂不可用');
    }
  }

  // 下载文件
  function downloadFile(file: any) {
    if (file.url) {
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      createMessage.info('文件下载功能暂不可用');
    }
  }

  // 删除文件
  function removeFile(index: number) {
    createConfirm({
      iconType: 'warning',
      title: '确认删除',
      content: `确定要删除文件 "${uploadedFiles.value[index].name}" 吗？`,
      onOk: () => {
        uploadedFiles.value.splice(index, 1);
        // 更新表单数据
        setAttachmentFieldsValue({ institutionAttachment: uploadedFiles.value });
        createMessage.success('文件删除成功');
      },
    });
  }

  // 获取初始表单数据
  function getInitialFormData() {
    return {
      institutionCode: '',
      institutionName: '',
      belongGroup: 0,
      belongCompany: '',
      manageCompany: '',
      reported: 0,
      handlerUserName: '张三',
      inputUserName: '张三',
      inputTime: formatDateTime(new Date()),
      approvalDate: '',
      effectiveDate: '',
      expiryDate: '',
      remark: '',
      status: 0,
      institutionAttachment: [],
    };
  }

  // 格式化日期时间
  function formatDateTime(date: any) {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hour = String(d.getHours()).padStart(2, '0');
    const minute = String(d.getMinutes()).padStart(2, '0');
    const second = String(d.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  }

  // 加载编辑数据
  function loadEditData() {
    if (isUpdate.value && recordId.value) {
      getDetail(recordId.value)
        .then((res) => {
          const data = res.result || res;
          setBasicFieldsValue(data);
          setAttachmentFieldsValue({ institutionAttachment: data.institutionAttachment || [] });
          originalStatus.value = data.status || null;
        })
        .catch((error) => {
          console.error('加载数据失败:', error);
          createMessage.error('加载数据失败');
        });
    } else {
      // 新增模式，设置默认值
      const defaultData = getInitialFormData();
      setBasicFieldsValue(defaultData);
      setAttachmentFieldsValue({ institutionAttachment: [] });
    }
  }

  // 验证日期范围
  function validateDateRange() {
    const basicData = getBasicFieldsValue();
    if (basicData?.effectiveDate && basicData?.expiryDate) {
      const startDate = new Date(basicData.effectiveDate);
      const endDate = new Date(basicData.expiryDate);
      if (endDate <= startDate) {
        createMessage.error('失效日期必须大于生效日期');
        return false;
      }
    }
    return true;
  }

  // 验证附件
  function validateAttachments() {
    const attachmentData = getAttachmentFieldsValue();
    if (!attachmentData?.institutionAttachment || attachmentData.institutionAttachment.length === 0) {
      createMessage.error('请上传至少一个制度附件');
      return false;
    }
    return true;
  }

  // 重置表单
  function handleReset() {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '确定要重置表单吗？所有已填写的数据将会丢失。',
      onOk: () => {
        if (isUpdate.value) {
          loadEditData();
        } else {
          const defaultData = getInitialFormData();
          setBasicFieldsValue(defaultData);
          setAttachmentFieldsValue({ institutionAttachment: [] });
        }
        createMessage.success('表单已重置！');
      },
    });
  }

  // 提交表单
  async function handleSubmit() {
    try {
      // 验证基本信息表单
      await validateBasic();
      // 验证附件信息表单
      await validateAttachment();
      // 验证日期范围
      if (!validateDateRange()) {
        return;
      }
      // 验证附件
      if (!validateAttachments()) {
        return;
      }

      loading.value = true;

      const basicData = getBasicFieldsValue() || {};
      const attachmentData = getAttachmentFieldsValue() || {};
      const formData = { ...basicData, ...attachmentData };

      // 如果是编辑模式，添加ID
      if (isUpdate.value && recordId.value) {
        formData.id = recordId.value;
      }

      const action = isUpdate.value ? '更新' : '新增';
      createMessage.info(`正在${action}数据...`);

      // 调用真实API
      try {
        const result = await saveOrUpdate(formData, isUpdate.value);
        console.log('提交结果：', result);

        const statusMap = {
          0: '草稿',
          1: '备案',
          2: '撤回',
          4: '作废',
        };

        createMessage.success(`${action}成功，状态为：${statusMap[formData.status]}！`);

        // 延迟跳转
        setTimeout(() => {
          router.push('/assetsUse/system');
        }, 1000);
      } catch (error) {
        console.error('提交失败：', error);
        createMessage.error(`${action}失败，请检查网络连接或联系管理员`);
      }
    } catch (error) {
      createMessage.error('表单验证失败，请检查并完善表单信息！');
    } finally {
      loading.value = false;
    }
  }

  // 监听状态变化，动态更新状态选项
  watch(
    () => {
      const fields = getBasicFieldsValue();
      return fields?.status;
    },
    (_newStatus) => {
      // 这里可以添加状态变化的逻辑
    }
  );

  // 监听文件上传变化
  watch(
    () => getAttachmentFieldsValue()?.institutionAttachment,
    (newFiles) => {
      if (newFiles && Array.isArray(newFiles)) {
        uploadedFiles.value = newFiles;
      }
    },
    { immediate: true }
  );

  onMounted(() => {
    // 从路由参数获取ID
    const id = route.params.id;
    if (id) {
      isUpdate.value = true;
      recordId.value = id as string;
    }

    // 加载数据
    loadEditData();
  });
</script>

<style lang="less" scoped>
  .system-form {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 16px;

    .simple-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-card {
      background: white;
      border-radius: 6px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .form-card-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .form-card-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .form-card-body {
        padding: 20px;
      }
    }

    .form-footer {
      background: white;
      padding: 16px 20px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    // 帮助文本样式
    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }

    // 文件上传提示信息样式
    .upload-tips {
      margin-top: 16px;
      padding: 12px 16px;
      background: #f6f8fa;
      border-radius: 6px;
      border-left: 4px solid #1890ff;

      .upload-tip-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 13px;
        color: #666;

        &:last-child {
          margin-bottom: 0;
        }

        .tip-icon {
          margin-right: 8px;
          color: #1890ff;
          font-size: 14px;
        }
      }
    }

    // 已上传文件列表样式
    .uploaded-files {
      margin-top: 20px;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      overflow: hidden;

      .files-title {
        padding: 12px 16px;
        background: #fafafa;
        border-bottom: 1px solid #e8e8e8;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        display: flex;
        align-items: center;

        .title-icon {
          margin-right: 8px;
          color: #1890ff;
        }
      }

      .files-list {
        .file-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #f0f0f0;
          transition: background-color 0.2s;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background-color: #f9f9f9;
          }

          .file-info {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;

            .file-icon {
              margin-right: 8px;
              font-size: 16px;
              color: #666;
            }

            .file-name {
              flex: 1;
              margin-right: 12px;
              color: #333;
              font-size: 14px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .file-size {
              color: #999;
              font-size: 12px;
              white-space: nowrap;
            }
          }

          .file-actions {
            display: flex;
            gap: 8px;

            .ant-btn-link {
              padding: 4px 8px;
              height: auto;
              font-size: 12px;

              .anticon {
                margin-right: 4px;
              }
            }
          }
        }
      }
    }
  }
</style>