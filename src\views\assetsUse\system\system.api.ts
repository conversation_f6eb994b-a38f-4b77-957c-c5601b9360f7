import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';
import type { InstitutionInfo, InstitutionQueryParams, InstitutionPageResult, ApiResponse } from './types';

enum Api {
  list = '/jeecg-boot/biz/institution/page',
  save = '/jeecg-boot/biz/institution/save',
  edit = '/jeecg-boot/biz/institution/save',
  delete = '/jeecg-boot/biz/institution/delete',
  deleteBatch = '/mock/system/deleteBatch',
  importExcel = '/mock/system/importExcel',
  exportXls = '/mock/system/exportXls',
  exportAll = '/mock/system/exportAll',
  downloadTemplate = '/mock/system/downloadTemplate',
  detail = '/jeecg-boot/biz/institution/detail',
}

/**
 * 列表接口 - 转换查询参数格式
 * @param params
 */
export const list = (params: any): Promise<ApiResponse<InstitutionPageResult>> => {
  // 转换前端查询参数为接口要求的格式
  const queryRo = {
    pageNo: params.pageNo || 1,
    pageSize: params.pageSize || 10,
    institutionCode: params.institutionCode || '',
    institutionName: params.institutionName || '',
    belongCompany: params.belongCompany || 0,
    manageCompany: params.manageCompany || 0,
    reported: params.reported || 0,
    status: params.status || 0,
    handlerUserName: params.handlerUserName || '',
    inputUserName: params.inputUserName || '',
    // 处理日期范围查询
    minApprovalDate: params.approvalDateRange?.[0] || '',
    maxApprovalDate: params.approvalDateRange?.[1] || '',
    minEffectiveDate: params.effectiveDateRange?.[0] || '',
    maxEffectiveDate: params.effectiveDateRange?.[1] || '',
    minExpiryDate: params.expiryDateRange?.[0] || '',
    maxExpiryDate: params.expiryDateRange?.[1] || '',
    minInputTime: params.inputTimeRange?.[0] || '',
    maxInputTime: params.inputTimeRange?.[1] || '',
    minUpdateTime: params.updateTimeRange?.[0] || '',
    maxUpdateTime: params.updateTimeRange?.[1] || '',
  };

  return defHttp.post({ url: Api.list, data: queryRo });
};

/**
 * 保存或更新 - 转换数据字段格式
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params: InstitutionInfo, isUpdate: boolean): Promise<ApiResponse<string>> => {
  // 转换前端数据字段为接口要求的格式
  const dto = {
    institutionCode: params.institutionCode || '',
    institutionName: params.institutionName || '',
    belongGroup: params.belongGroup || 0,
    belongCompany: params.belongCompany || 0,
    manageCompany: params.manageCompany || 0,
    reported: params.reported || 0,
    handlerUserName: params.handlerUserName || '',
    inputUserName: params.inputUserName || '',
    inputTime: params.inputTime || '',
    approvalDate: params.approvalDate || '',
    effectiveDate: params.effectiveDate || '',
    expiryDate: params.expiryDate || '',
    status: params.status || 0,
    remark: params.remark || '',
    // 转换附件格式
    institutionAttachment: (params.institutionAttachment || []).map(file => ({
      name: file.name || '',
      url: file.url || ''
    }))
  };

  // 如果是编辑模式，需要传递ID
  if (isUpdate && params.id) {
    dto.id = params.id;
  }

  // 新增和编辑都使用同一个保存接口
  return defHttp.post({ url: Api.save, data: dto });
};

/**
 * 获取详情 - 转换返回数据格式
 * @param id
 */
export const getDetail = (id: string | number): Promise<ApiResponse<InstitutionInfo>> => {
  return defHttp.get({ url: Api.detail, params: { id } }).then(res => {
    // 转换后端数据字段为前端格式
    if (res.result) {
      const data = res.result;
      return {
        ...res,
        result: {
          institutionCode: data.institutionCode || '',
          institutionName: data.institutionName || '',
          belongGroup: data.belongGroup || 0,
          belongCompany: data.belongCompany || 0,
          manageCompany: data.manageCompany || 0,
          reported: data.reported || 0,
          handlerUserName: data.handlerUserName || '',
          inputUserName: data.inputUserName || '',
          inputTime: data.inputTime || '',
          approvalDate: data.approvalDate || '',
          effectiveDate: data.effectiveDate || '',
          expiryDate: data.expiryDate || '',
          status: data.status || 0,
          remark: data.remark || '',
          // 转换附件格式
          institutionAttachment: (data.institutionAttachment || []).map(file => ({
            name: file.name || '',
            url: file.url || '',
            size: file.size || 0
          }))
        }
      };
    }
    return res;
  });
};

/**
 * 删除 - 使用GET方式
 * @param params
 * @param handleSuccess
 */
export const deleteSystem = (params, handleSuccess) => {
  return defHttp.get({ url: Api.delete, params: { id: params.id } }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDeleteSystem = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 导入
 * @param params
 */
export const importExcel = (params) => defHttp.post({ url: Api.importExcel, params });

/**
 * 导出
 * @param params
 */
export const exportSystem = (params) => defHttp.get({ url: Api.exportXls, params, responseType: 'blob' });

/**
 * 全部导出
 * @param params
 */
export const exportAllSystem = (params) => defHttp.get({ url: Api.exportAll, params, responseType: 'blob' });

/**
 * 下载导入模板
 */
export const downloadTemplate = () => defHttp.get({ url: Api.downloadTemplate, responseType: 'blob' }); 