import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '制度编号',
    dataIndex: 'institutionCode',
    width: 120,
    fixed: 'left',
  },
  {
    title: '制度名称',
    dataIndex: 'institutionName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '所属集团',
    dataIndex: 'belongGroup',
    width: 250,
    ellipsis: true,
    customRender: ({ text }) => {
      // 根据接口文档，belongGroup是字符串类型
      return text || '-';
    },
  },
  {
    title: '所属企业',
    dataIndex: 'belongCompany',
    width: 250,
    ellipsis: true,
    customRender: ({ text }) => {
      const companyMap = {
        0: '厦门市城市建设发展投资有限公司',
        1: '厦门市地热资源管理有限公司',
        2: '厦门兴地房屋征迁服务有限公司',
        3: '厦门地丰置业有限公司',
        4: '图智策划咨询（厦门）有限公司',
        5: '厦门市集众祥和物业管理有限公司',
        6: '厦门市人居乐业物业服务有限公司',
      };
      return companyMap[text] || '-';
    },
  },
  {
    title: '管理单位',
    dataIndex: 'manageCompanyName',
    width: 250,
    ellipsis: true,
    // 接口返回的是manageCompanyName字符串，直接显示
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reported',
    width: 150,
    customRender: ({ text }) => {
      return text === 1 ? render.renderTag('是', 'success') : render.renderTag('否', 'default');
    },
  },
  {
    title: '经办人',
    dataIndex: 'handlerUserName',
    width: 100,
  },
  {
    title: '审批日期',
    dataIndex: 'approvalDate',
    width: 120,
  },
  {
    title: '生效日期',
    dataIndex: 'effectiveDate',
    width: 120,
  },
  {
    title: '失效日期',
    dataIndex: 'expiryDate',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        0: { text: '草稿', color: 'default' },
        1: { text: '备案', color: 'success' },
        2: { text: '撤回', color: 'warning' },
        4: { text: '作废', color: 'error' },
      };
      const status = statusMap[text];
      return status ? render.renderTag(status.text, status.color) : '';
    },
  },
  {
    title: '附件',
    dataIndex: 'institutionAttachment',
    width: 100,
    customRender: ({ text }) => {
      if (!text || !Array.isArray(text)) return '-';
      return render.renderTag(`${text.length}个附件`, 'default');
    },
  },
  {
    title: '录入人',
    dataIndex: 'inputUserName',
    width: 100,
  },
  {
    title: '录入时间',
    dataIndex: 'inputTime',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    ellipsis: true,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'institutionCode',
    label: '制度编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入制度编号',
    },
  },
  {
    field: 'institutionName',
    label: '制度名称',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入制度名称',
    },
  },
  {
    field: 'belongCompany',
    label: '所属企业',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择所属企业',
      options: [
        { label: '厦门市城市建设发展投资有限公司', value: 0 },
        { label: '厦门市地热资源管理有限公司', value: 1 },
        { label: '厦门兴地房屋征迁服务有限公司', value: 2 },
        { label: '厦门地丰置业有限公司', value: 3 },
        { label: '图智策划咨询（厦门）有限公司', value: 4 },
        { label: '厦门市集众祥和物业管理有限公司', value: 5 },
        { label: '厦门市人居乐业物业服务有限公司', value: 6 },
      ],
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '草稿', value: 0 },
        { label: '备案', value: 1 },
        { label: '撤回', value: 2 },
        { label: '作废', value: 4 },
      ],
    },
  },
  {
    field: 'approvalDateRange',
    label: '审批日期',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'effectiveDateRange',
    label: '生效日期',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'expiryDateRange',
    label: '失效日期',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'manageCompany',
    label: '管理单位',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      placeholder: '请选择管理单位',
      options: [
        { label: '厦门市城市建设发展投资有限公司', value: 0 },
        { label: '厦门市地热资源管理有限公司', value: 1 },
        { label: '厦门兴地房屋征迁服务有限公司', value: 2 },
        { label: '厦门地丰置业有限公司', value: 3 },
        { label: '图智策划咨询（厦门）有限公司', value: 4 },
        { label: '厦门市集众祥和物业管理有限公司', value: 5 },
        { label: '厦门市人居乐业物业服务有限公司', value: 6 },
      ],
    },
  },
  {
    field: 'reported',
    label: '是否报送国资委',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
  },
  {
    field: 'handlerUserName',
    label: '经办人',
    component: 'Input',
    colProps: { span: 8 },
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    field: 'inputUserName',
    label: '录入人',
    component: 'Input',
    colProps: { span: 8 },
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    field: 'inputTimeRange',
    label: '录入时间',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'updateTimeRange',
    label: '更新时间',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
]; 