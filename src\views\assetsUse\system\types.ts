// 制度信息相关类型定义

// 附件信息
export interface AttachmentInfo {
  name: string;
  url: string;
  size?: number;
}

// 制度信息基本数据
export interface InstitutionInfo {
  id?: number;
  institutionCode?: string;
  institutionName?: string;
  belongGroup?: number | string;
  belongCompany?: number;
  manageCompany?: number;
  manageCompanyName?: string;
  reported?: number;
  handlerUserName?: string;
  inputUserName?: string;
  inputTime?: string;
  approvalDate?: string;
  effectiveDate?: string;
  expiryDate?: string;
  status?: number;
  remark?: string;
  institutionAttachment?: AttachmentInfo[];
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
}

// 查询参数
export interface InstitutionQueryParams {
  pageNo?: number;
  pageSize?: number;
  institutionCode?: string;
  institutionName?: string;
  belongCompany?: number;
  manageCompany?: number;
  reported?: number;
  status?: number;
  handlerUserName?: string;
  inputUserName?: string;
  minApprovalDate?: string;
  maxApprovalDate?: string;
  minEffectiveDate?: string;
  maxEffectiveDate?: string;
  minExpiryDate?: string;
  maxExpiryDate?: string;
  minInputTime?: string;
  maxInputTime?: string;
  minUpdateTime?: string;
  maxUpdateTime?: string;
}

// 分页响应数据
export interface InstitutionPageResult {
  current: number;
  pages: number;
  records: InstitutionInfo[];
  size: number;
  total: number;
}

// API响应格式
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  result: T;
  success: boolean;
  timestamp: number;
}
